// tests/amocrm-integration.test.js
import {expect} from 'chai';
import sinon from 'sinon';
import axios from 'axios';
import AmoCRMIntegration from '../integrations/amocrm.js';

describe('AmoCRM Integration', () => {
    let integration;
    let axiosStub;
    
    const mockConfig = {
        domain: 'test.amocrm.ru',
        token: 'test-token',
        telegram_id_field_id: '123',
        telegram_username_field_id: '456',
        drive_url: 'https://drive-test.amocrm.ru'
    };

    beforeEach(() => {
        integration = new AmoCRMIntegration(mockConfig);
        axiosStub = sinon.stub(axios, 'request');
    });

    afterEach(() => {
        sinon.restore();
    });

    describe('Configuration', () => {
        it('should initialize with correct configuration', () => {
            expect(integration.domain).to.equal('test.amocrm.ru');
            expect(integration.token).to.equal('test-token');
            expect(integration.driveUrl).to.equal('https://drive-test.amocrm.ru');
        });

        it('should validate configuration correctly', () => {
            const validConfig = {
                domain: 'test.amocrm.ru',
                token: 'test-token'
            };
            
            const result = integration.validateConfig(validConfig);
            expect(result.valid).to.be.true;
            expect(result.errors).to.be.empty;
        });

        it('should reject invalid configuration', () => {
            const invalidConfig = {
                domain: 'invalid-domain'
                // missing token
            };
            
            const result = integration.validateConfig(invalidConfig);
            expect(result.valid).to.be.false;
            expect(result.errors).to.include('token is required');
        });
    });

    describe('Drive URL Discovery', () => {
        it('should discover drive URL when not provided', async () => {
            const integrationWithoutDriveUrl = new AmoCRMIntegration({
                domain: 'test.amocrm.ru',
                token: 'test-token'
            });

            axiosStub.resolves({
                data: {
                    drive_url: 'https://drive-discovered.amocrm.ru'
                }
            });

            const driveUrl = await integrationWithoutDriveUrl.ensureDriveUrl();
            
            expect(driveUrl).to.equal('https://drive-discovered.amocrm.ru');
            expect(integrationWithoutDriveUrl.driveUrl).to.equal('https://drive-discovered.amocrm.ru');
        });

        it('should use existing drive URL if available', async () => {
            const driveUrl = await integration.ensureDriveUrl();
            
            expect(driveUrl).to.equal('https://drive-test.amocrm.ru');
            expect(axiosStub.called).to.be.false; // Should not make API call
        });
    });

    describe('File Operations', () => {
        it('should generate proper analysis file names', () => {
            const fileName = integration.generateAnalysisFileName('Test User');
            
            expect(fileName).to.match(/^analysis_report_Test_User_\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}\.txt$/);
        });

        it('should sanitize contact names in file names', () => {
            const fileName = integration.generateAnalysisFileName('Test@User#123!');
            
            expect(fileName).to.include('Test_User_123_');
            expect(fileName).to.not.include('@');
            expect(fileName).to.not.include('#');
            expect(fileName).to.not.include('!');
        });

        it('should upload analysis file successfully', async () => {
            const mockSessionResponse = {
                upload_url: 'https://upload.example.com/session123',
                session_id: 12345
            };

            const mockUploadResponse = {
                data: {
                    uuid: 'file-uuid-123',
                    name: 'analysis_report_test.txt'
                }
            };

            // Mock session creation
            axiosStub.onFirstCall().resolves({
                data: mockSessionResponse
            });

            // Mock file upload
            const axiosPostStub = sinon.stub(axios, 'post').resolves(mockUploadResponse);

            const result = await integration.uploadAnalysisFile('Test analysis content', 'Test User');

            expect(result.success).to.be.true;
            expect(result.fileUuid).to.equal('file-uuid-123');
            expect(axiosPostStub.calledOnce).to.be.true;
            
            axiosPostStub.restore();
        });
    });

    describe('Contact Operations', () => {
        it('should format contact data correctly without analysis field', () => {
            const testData = {
                contact: {
                    name: 'Test User',
                    externalId: '12345'
                },
                analysis: 'This should not be in custom fields'
            };

            const contactData = integration.formatContactData(testData);

            expect(contactData.name).to.equal('Test User');
            expect(contactData.custom_fields_values).to.be.an('array');
            
            // Should not include analysis in custom fields
            const analysisField = contactData.custom_fields_values.find(
                field => field.field_id === integration.analysisReportFieldId
            );
            expect(analysisField).to.be.undefined;
        });

        it('should include telegram fields when configured', () => {
            const testData = {
                contact: {
                    name: 'Test User',
                    externalId: '12345'
                }
            };

            const contactData = integration.formatContactData(testData);

            // Should include telegram ID field
            const telegramIdField = contactData.custom_fields_values.find(
                field => field.field_id === 123
            );
            expect(telegramIdField).to.exist;
            expect(telegramIdField.values[0].value).to.equal('12345');

            // Should include telegram username field
            const telegramUsernameField = contactData.custom_fields_values.find(
                field => field.field_id === 456
            );
            expect(telegramUsernameField).to.exist;
            expect(telegramUsernameField.values[0].value).to.equal('Test User');
        });
    });

    describe('Integration Flow', () => {
        it('should handle file upload failure gracefully', async () => {
            // Mock drive URL discovery
            axiosStub.onFirstCall().resolves({
                data: { drive_url: 'https://drive-test.amocrm.ru' }
            });

            // Mock session creation failure
            axiosStub.onSecondCall().rejects(new Error('Session creation failed'));

            const result = await integration.uploadAnalysisFile('Test analysis', 'Test User');

            expect(result.success).to.be.false;
            expect(result.error).to.include('Session creation failed');
        });

        it('should test connection successfully', async () => {
            axiosStub.resolves({
                data: {
                    _embedded: {
                        contacts: []
                    }
                }
            });

            const result = await integration.testConnection();
            expect(result).to.be.true;
        });
    });
});
