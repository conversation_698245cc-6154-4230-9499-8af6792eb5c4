import IntegrationInterface from './base.js';
import axios from 'axios';
import {log} from '../library/log.js';
import {getStorage} from '../library/storage.js';

/**
 * AmoCRM Integration for Dialog Analyzer
 * Works with contacts instead of leads and uses long-term token
 */
class AmoCRMIntegration extends IntegrationInterface {
    constructor(config) {
        super(config);
        this.domain = config.domain;
        this.token = config.token;
        this.telegramIdFieldId = config.telegram_id_field_id;
        this.telegramUsernameFieldId = config.telegram_username_field_id;
        this.analysisReportFieldId = config.analysis_report_field_id;
    }

    /**
     * Send analysis data to AmoCRM (create or update contact)
     */
    async send(data) {
        try {
            log.info('Sending analysis data to AmoCRM', {data}, 'send', 'AmoCRMIntegration');

            // Try to find existing contact first
            const existingContact = await this.findContactByTelegramData(data.contact.id);

            if (existingContact) {
                // Update existing contact with analysis
                return await this.update(existingContact, data);
            } else {
                // Create new contact with analysis
                return await this.createContactWithAnalysis(data);
            }
        } catch (error) {
            log.error('Error sending data to AmoCRM', {
                error: error.message,
                data
            }, 'send', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Update contact analysis in AmoCRM
     */
    async update(externalId, data) {
        try {
            log.info('Updating contact analysis in AmoCRM', {externalId, data}, 'update', 'AmoCRMIntegration');

            return await this.updateContactAnalysis(parseInt(externalId), data.analysis);
        } catch (error) {
            log.error('Error updating data in AmoCRM', {
                error: error.message,
                externalId,
                data
            }, 'update', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Delete contact from AmoCRM (not typically used for dialog analyzer)
     */
    async delete(externalId) {
        try {
            log.info('Deleting contact from AmoCRM', {externalId}, 'delete', 'AmoCRMIntegration');

            const response = await this.makeRequest(`contacts/${externalId}`, 'DELETE');

            return {
                success: response.status === 204 || response.status === 200,
                data: response
            };
        } catch (error) {
            log.error('Error deleting contact from AmoCRM', {
                error: error.message,
                externalId
            }, 'delete', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Find contacts in AmoCRM
     */
    async find(query) {
        try {
            log.info('Finding contacts in AmoCRM', {query}, 'find', 'AmoCRMIntegration');

            const params = new URLSearchParams();
            if (query.name) params.append('query', query.name);
            if (query.email) params.append('query', query.email);
            if (query.phone) params.append('query', query.phone);

            const response = await this.makeRequest(`contacts?${params.toString()}`, 'GET');

            return {
                success: true,
                data: response._embedded?.contacts || []
            };
        } catch (error) {
            log.error('Error finding contacts in AmoCRM', {
                error: error.message,
                query
            }, 'find', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Test connection to AmoCRM
     */
    async testConnection() {
        try {
            const response = await this.makeRequest('contacts?limit=1', 'GET');
            return !!response;
        } catch (error) {
            log.error('AmoCRM connection test failed', {
                error: error.message
            }, 'testConnection', 'AmoCRMIntegration');
            return false;
        }
    }

    /**
     * Get configuration schema
     */
    getConfigSchema() {
        return {
            domain: {
                type: 'string',
                required: true,
                description: 'AmoCRM domain (e.g., your-domain.amocrm.ru)'
            },
            token: {
                type: 'string',
                required: true,
                description: 'Long-term access token'
            },
            telegram_id_field_id: {
                type: 'string',
                required: false,
                description: 'ID of custom field for Telegram ID (optional)'
            },
            telegram_username_field_id: {
                type: 'string',
                required: false,
                description: 'ID of custom field for Telegram username (optional)'
            },
            analysis_report_field_id: {
                type: 'string',
                required: true,
                description: 'ID of custom field for analysis report (text field)'
            }
        };
    }

    /**
     * Validate configuration
     */
    validateConfig(config) {
        const errors = [];
        const required = ['domain', 'token', 'analysis_report_field_id'];

        required.forEach(field => {
            if (!config[field]) {
                errors.push(`${field} is required`);
            }
        });

        if (config.domain && !config.domain.includes('.amocrm.')) {
            errors.push('Invalid AmoCRM domain format');
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * Make API request to AmoCRM
     */
    async makeRequest(endpoint, method = 'GET', data = null) {
        const url = `https://${this.domain}/api/v4/${endpoint}`;

        const config = {
            method,
            url,
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Content-Type': 'application/json'
            },
            timeout: 30000
        };
        let logMeta = {
            url,
            method,
            data
        }

        if (data && (method === 'POST' || method === 'PATCH')) {
            config.data = data;
        }

        try {
            const response = await axios(config);
            log.info('AmoCrm response:', {...logMeta, status: response.status, data: response.data, headers: response.headers}, 'makeRequest', 'AmoCRMIntegration');
            return response.data;
        } catch (error) {
            if (error.response) {
                log.error('AmoCrm Error Response:', {...logMeta,
                    status: error.response.status,
                    data: error.response.data,
                    headers: error.response.headers,

                }, 'makeRequest', 'AmoCRMIntegration');
            } else if (error.request) {
                log.error('No Response Received:', {...logMeta,err:error.request}, 'makeRequest', 'AmoCRMIntegration');
            } else {
                console.error('Axios Config/Error:', {...logMeta,err:error.message}, 'makeRequest', 'AmoCRMIntegration');
            }
            throw error;
        }
    }

    /**
     * Find contact by Telegram data (ID or username)
     */
    async findContactByTelegramData(contactId) {
        try {
            log.info('Finding contact by Telegram data', {contactId}, 'findContactByTelegramData', 'AmoCRMIntegration');
            const userStorage = getStorage('ExternalUser', contactId);
            return await userStorage.get('d_a.contact', null);
        } catch (error) {
            log.error('Error finding contact by Telegram data', {
                error: error.message,
                contactId
            }, 'findContactByTelegramData', 'AmoCRMIntegration');
            return null;
        }
    }

    /**
     * Create new contact with analysis
     */
    async createContactWithAnalysis(data) {
        try {
            log.info('Creating contact with analysis', {data}, 'createContactWithAnalysis', 'AmoCRMIntegration');

            const contactData = this.formatContactData(data);
            const response = await this.makeRequest('contacts', 'POST', [contactData]);

            if (response._embedded && response._embedded.contacts && response._embedded.contacts[0]) {
                const contact = response._embedded.contacts[0];

                // Store contact ID for future use
                const userStorage = getStorage('ExternalUser', data.contact.id);
                await userStorage.set('d_a.contact', contact.id);

                return {
                    success: true,
                    externalId: contact.id.toString(),
                    data: response
                };
            } else {
                throw new Error('Failed to create contact in AmoCRM');
            }
        } catch (error) {
            log.error('Error creating contact with analysis', {
                error: error.message,
                data
            }, 'createContactWithAnalysis', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Update contact analysis
     */
    async updateContactAnalysis(contactId, analysis) {
        try {
            log.info('Updating contact analysis', {contactId, analysis}, 'updateContactAnalysis', 'AmoCRMIntegration');

            const updateData = {
                id: contactId,
                custom_fields_values: [{
                    field_id: parseInt(this.analysisReportFieldId),
                    values: [{
                        value: analysis
                    }]
                }]
            };

            const response = await this.makeRequest('contacts', 'PATCH', [updateData]);

            return {
                success: !!response._embedded?.contacts?.[0],
                externalId: contactId.toString(),
                data: response
            };
        } catch (error) {
            log.error('Error updating contact analysis', {
                error: error.message,
                contactId,
                analysis
            }, 'updateContactAnalysis', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Format contact data for AmoCRM
     */
    formatContactData(data) {
        const contactData = {
            name: data.contact.name || 'Dialog User',
            custom_fields_values: []
        };

        // Add analysis report
        if (data.analysis) {
            contactData.custom_fields_values.push({
                field_id: parseInt(this.analysisReportFieldId),
                values: [{
                    value: data.analysis
                }]
            });
        }

        // Add Telegram ID if field is configured
        if (this.telegramIdFieldId && data.contact.externalId) {
            contactData.custom_fields_values.push({
                field_id: parseInt(this.telegramIdFieldId),
                values: [{
                    value: data.contact.externalId
                }]
            });
        }

        // Add Telegram username if field is configured
        if (this.telegramUsernameFieldId && data.contact.name) {
            contactData.custom_fields_values.push({
                field_id: parseInt(this.telegramUsernameFieldId),
                values: [{
                    value: data.contact.name
                }]
            });
        }

        return contactData;
    }
}

export default AmoCRMIntegration;
