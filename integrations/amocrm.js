import IntegrationInterface from './base.js';
import axios from 'axios';
import {log} from '../library/log.js';
import {getStorage} from '../library/storage.js';

/**
 * AmoCRM Integration for Dialog Analyzer
 * Works with contacts instead of leads and uses long-term token
 * Uses Files API to upload analysis reports as files instead of text fields
 */
class AmoCRMIntegration extends IntegrationInterface {
    constructor(config) {
        super(config);
        this.domain = config.domain;
        this.token = config.token;
        this.telegramIdFieldId = config.telegram_id_field_id;
        this.telegramUsernameFieldId = config.telegram_username_field_id;
        this.driveUrl = config.drive_url || null;
        // Keep for backward compatibility, but will be deprecated
        this.analysisReportFieldId = config.analysis_report_field_id;
    }

    /**
     * Send analysis data to AmoCRM (create or update contact)
     */
    async send(data) {
        try {
            log.info('Sending analysis data to AmoCRM', {data}, 'send', 'AmoCRMIntegration');

            // Ensure we have drive URL for file operations
            await this.ensureDriveUrl();

            // Try to find existing contact first
            const existingContact = await this.findContactByTelegramData(data.contact.id);

            if (existingContact) {
                // Update existing contact with analysis
                return await this.update(existingContact, data);
            } else {
                // Create new contact with analysis
                return await this.createContactWithAnalysis(data);
            }
        } catch (error) {
            log.error('Error sending data to AmoCRM', {
                error: error.message,
                data
            }, 'send', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Update contact analysis in AmoCRM
     */
    async update(externalId, data) {
        try {
            log.info('Updating contact analysis in AmoCRM', {externalId, data}, 'update', 'AmoCRMIntegration');

            // Ensure we have drive URL for file operations
            await this.ensureDriveUrl();

            return await this.updateContactAnalysis(parseInt(externalId), data.analysis);
        } catch (error) {
            log.error('Error updating data in AmoCRM', {
                error: error.message,
                externalId,
                data
            }, 'update', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Delete contact from AmoCRM (not typically used for dialog analyzer)
     */
    async delete(externalId) {
        try {
            log.info('Deleting contact from AmoCRM', {externalId}, 'delete', 'AmoCRMIntegration');

            const response = await this.makeRequest(`contacts/${externalId}`, 'DELETE');

            return {
                success: response.status === 204 || response.status === 200,
                data: response
            };
        } catch (error) {
            log.error('Error deleting contact from AmoCRM', {
                error: error.message,
                externalId
            }, 'delete', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Find contacts in AmoCRM
     */
    async find(query) {
        try {
            log.info('Finding contacts in AmoCRM', {query}, 'find', 'AmoCRMIntegration');

            const params = new URLSearchParams();
            if (query.name) params.append('query', query.name);
            if (query.email) params.append('query', query.email);
            if (query.phone) params.append('query', query.phone);

            const response = await this.makeRequest(`contacts?${params.toString()}`, 'GET');

            return {
                success: true,
                data: response._embedded?.contacts || []
            };
        } catch (error) {
            log.error('Error finding contacts in AmoCRM', {
                error: error.message,
                query
            }, 'find', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Test connection to AmoCRM
     */
    async testConnection() {
        try {
            const response = await this.makeRequest('contacts?limit=1', 'GET');
            return !!response;
        } catch (error) {
            log.error('AmoCRM connection test failed', {
                error: error.message
            }, 'testConnection', 'AmoCRMIntegration');
            return false;
        }
    }

    /**
     * Get configuration schema
     */
    getConfigSchema() {
        return {
            domain: {
                type: 'string',
                required: true,
                description: 'AmoCRM domain (e.g., your-domain.amocrm.ru)'
            },
            token: {
                type: 'string',
                required: true,
                description: 'Long-term access token'
            },
            telegram_id_field_id: {
                type: 'string',
                required: false,
                description: 'ID of custom field for Telegram ID (optional)'
            },
            telegram_username_field_id: {
                type: 'string',
                required: false,
                description: 'ID of custom field for Telegram username (optional)'
            },
            drive_url: {
                type: 'string',
                required: false,
                description: 'AmoCRM drive service URL (auto-discovered if not provided)'
            },
            analysis_report_field_id: {
                type: 'string',
                required: false,
                description: 'DEPRECATED: ID of custom field for analysis report (use file uploads instead)'
            }
        };
    }

    /**
     * Validate configuration
     */
    validateConfig(config) {
        const errors = [];
        const required = ['domain', 'token'];

        required.forEach(field => {
            if (!config[field]) {
                errors.push(`${field} is required`);
            }
        });

        if (config.domain && !config.domain.includes('.amocrm.')) {
            errors.push('Invalid AmoCRM domain format');
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * Make API request to AmoCRM
     */
    async makeRequest(endpoint, method = 'GET', data = null) {
        const url = `https://${this.domain}/api/v4/${endpoint}`;

        const config = {
            method,
            url,
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Content-Type': 'application/json'
            },
            timeout: 30000
        };
        let logMeta = {
            url,
            method,
            data
        }

        if (data && (method === 'POST' || method === 'PATCH')) {
            config.data = data;
        }

        try {
            const response = await axios(config);
            log.info('AmoCrm response:', {
                ...logMeta,
                status: response.status,
                data: response.data,
                headers: response.headers
            }, 'makeRequest', 'AmoCRMIntegration');
            return response.data;
        } catch (error) {
            if (error.response) {
                log.error('AmoCrm Error Response:', {
                    ...logMeta,
                    status: error.response.status,
                    data: error.response.data,
                    headers: error.response.headers,

                }, 'makeRequest', 'AmoCRMIntegration');
            } else if (error.request) {
                log.error('No Response Received:', {
                    ...logMeta,
                    err: error.request
                }, 'makeRequest', 'AmoCRMIntegration');
            } else {
                console.error('Axios Config/Error:', {
                    ...logMeta,
                    err: error.message
                }, 'makeRequest', 'AmoCRMIntegration');
            }
            throw error;
        }
    }

    /**
     * Make API request to AmoCRM Files service
     */
    async makeFileRequest(endpoint, method = 'GET', data = null, headers = {}) {
        if (!this.driveUrl) {
            throw new Error('Drive URL not available for file operations');
        }

        const url = `${this.driveUrl}/${endpoint}`;

        const config = {
            method,
            url,
            headers: {
                'Authorization': `Bearer ${this.token}`,
                ...headers
            },
            timeout: 60000 // Longer timeout for file operations
        };

        if (data) {
            if (method === 'POST' || method === 'PATCH') {
                if (headers['Content-Type'] === 'application/json') {
                    config.data = data;
                } else {
                    // For file uploads, data is already in the correct format
                    config.data = data;
                }
            }
        }

        try {
            const response = await axios(config);
            log.info('AmoCRM Files API response:', {
                url,
                method,
                status: response.status
            }, 'makeFileRequest', 'AmoCRMIntegration');
            return response.data;
        } catch (error) {
            log.error('AmoCRM Files API error:', {
                url,
                method,
                error: error.response?.data || error.message
            }, 'makeFileRequest', 'AmoCRMIntegration');
            throw error;
        }
    }

    /**
     * Ensure drive URL is available for file operations
     */
    async ensureDriveUrl() {
        if (this.driveUrl) {
            return this.driveUrl;
        }

        try {
            log.info('Discovering AmoCRM drive URL', {}, 'ensureDriveUrl', 'AmoCRMIntegration');

            const response = await this.makeRequest('account?with=drive_url', 'GET');

            if (response.drive_url) {
                this.driveUrl = response.drive_url;
                log.info('Drive URL discovered', {driveUrl: this.driveUrl}, 'ensureDriveUrl', 'AmoCRMIntegration');
                return this.driveUrl;
            } else {
                throw new Error('Drive URL not available in account response');
            }
        } catch (error) {
            log.error('Error discovering drive URL', {
                error: error.message
            }, 'ensureDriveUrl', 'AmoCRMIntegration');
            throw new Error(`Failed to discover drive URL: ${error.message}`);
        }
    }

    /**
     * Find contact by Telegram data (ID or username)
     */
    async findContactByTelegramData(contactId) {
        try {
            log.info('Finding contact by Telegram data', {contactId}, 'findContactByTelegramData', 'AmoCRMIntegration');
            const userStorage = getStorage('ExternalUser', contactId);
            return await userStorage.get('d_a.contact', null);
        } catch (error) {
            log.error('Error finding contact by Telegram data', {
                error: error.message,
                contactId
            }, 'findContactByTelegramData', 'AmoCRMIntegration');
            return null;
        }
    }

    /**
     * Upload analysis report as file to AmoCRM
     */
    async uploadAnalysisFile(analysis, contactName = 'Unknown') {
        try {
            const fileName = this.generateAnalysisFileName(contactName);
            const fileContent = Buffer.from(analysis, 'utf8');
            const fileSize = fileContent.length;

            log.info('Uploading analysis file', {
                fileName,
                fileSize
            }, 'uploadAnalysisFile', 'AmoCRMIntegration');

            // Create upload session
            const sessionData = {
                file_name: fileName,
                file_size: fileSize,
                content_type: 'text/plain'
            };

            const session = await this.makeFileRequest('v1.0/sessions', 'POST', sessionData, {
                'Content-Type': 'application/json'
            });

            if (!session.upload_url) {
                throw new Error('Failed to create upload session');
            }

            // Upload file content
            const uploadResponse = await axios.post(session.upload_url, fileContent, {
                headers: {
                    'Content-Type': 'text/plain'
                },
                timeout: 60000
            });

            if (uploadResponse.data.uuid) {
                log.info('Analysis file uploaded successfully', {
                    fileUuid: uploadResponse.data.uuid,
                    fileName
                }, 'uploadAnalysisFile', 'AmoCRMIntegration');

                return {
                    success: true,
                    fileUuid: uploadResponse.data.uuid,
                    fileName: fileName
                };
            } else {
                throw new Error('File upload completed but no UUID returned');
            }
        } catch (error) {
            log.error('Error uploading analysis file', {
                error: error.message
            }, 'uploadAnalysisFile', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Generate filename for analysis report
     */
    generateAnalysisFileName(contactName) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const sanitizedName = contactName.replace(/[^a-zA-Z0-9]/g, '_').slice(0, 20);
        return `analysis_report_${sanitizedName}_${timestamp}.txt`;
    }

    /**
     * Attach file to contact
     */
    async attachFileToContact(contactId, fileUuid) {
        try {
            log.info('Attaching file to contact', {
                contactId,
                fileUuid
            }, 'attachFileToContact', 'AmoCRMIntegration');

            const attachData = [{
                file_uuid: fileUuid
            }];

            await this.makeRequest(`contacts/${contactId}/files`, 'PUT', attachData);

            log.info('File attached to contact successfully', {
                contactId,
                fileUuid
            }, 'attachFileToContact', 'AmoCRMIntegration');

            return {
                success: true
            };
        } catch (error) {
            log.error('Error attaching file to contact', {
                error: error.message,
                contactId,
                fileUuid
            }, 'attachFileToContact', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Create new contact with analysis
     */
    async createContactWithAnalysis(data) {
        try {
            log.info('Creating contact with analysis', {data}, 'createContactWithAnalysis', 'AmoCRMIntegration');

            const contactData = this.formatContactData(data);
            const response = await this.makeRequest('contacts', 'POST', [contactData]);

            if (response._embedded && response._embedded.contacts && response._embedded.contacts[0]) {
                const contact = response._embedded.contacts[0];

                // Store contact ID for future use
                const userStorage = getStorage('ExternalUser', data.contact.id);
                await userStorage.set('d_a.contact', contact.id);

                // Upload analysis as file if provided
                if (data.analysis) {
                    const fileResult = await this.uploadAnalysisFile(data.analysis, data.contact.name);
                    if (fileResult.success) {
                        await this.attachFileToContact(contact.id, fileResult.fileUuid);
                    } else {
                        log.warn('Failed to upload analysis file, but contact was created', {
                            contactId: contact.id,
                            error: fileResult.error
                        }, 'createContactWithAnalysis', 'AmoCRMIntegration');
                    }
                }

                return {
                    success: true,
                    externalId: contact.id.toString(),
                    data: response
                };
            } else {
                throw new Error('Failed to create contact in AmoCRM');
            }
        } catch (error) {
            log.error('Error creating contact with analysis', {
                error: error.message,
                data
            }, 'createContactWithAnalysis', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Update contact analysis
     */
    async updateContactAnalysis(contactId, analysis) {
        try {
            log.info('Updating contact analysis', {contactId}, 'updateContactAnalysis', 'AmoCRMIntegration');

            // Get contact name for file naming
            let contactName = 'Unknown';
            try {
                const contactResponse = await this.makeRequest(`contacts/${contactId}`, 'GET');
                contactName = contactResponse.name || 'Unknown';
            } catch (error) {
                log.warn('Could not fetch contact name for file naming', {
                    contactId,
                    error: error.message
                }, 'updateContactAnalysis', 'AmoCRMIntegration');
            }

            // Upload analysis as file
            const fileResult = await this.uploadAnalysisFile(analysis, contactName);
            if (!fileResult.success) {
                throw new Error(`Failed to upload analysis file: ${fileResult.error}`);
            }

            // Attach file to contact
            const attachResult = await this.attachFileToContact(contactId, fileResult.fileUuid);
            if (!attachResult.success) {
                throw new Error(`Failed to attach file to contact: ${attachResult.error}`);
            }

            return {
                success: true,
                externalId: contactId.toString(),
                fileUuid: fileResult.fileUuid,
                fileName: fileResult.fileName
            };
        } catch (error) {
            log.error('Error updating contact analysis', {
                error: error.message,
                contactId
            }, 'updateContactAnalysis', 'AmoCRMIntegration');

            // Fallback to text field if file upload fails and field is configured
            if (this.analysisReportFieldId) {
                log.info('Falling back to text field for analysis', {contactId}, 'updateContactAnalysis', 'AmoCRMIntegration');
                return await this.updateContactAnalysisTextField(contactId, analysis);
            }

            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Fallback method to update analysis using text field (for backward compatibility)
     */
    async updateContactAnalysisTextField(contactId, analysis) {
        try {
            log.info('Updating contact analysis via text field', {contactId}, 'updateContactAnalysisTextField', 'AmoCRMIntegration');

            // Truncate analysis to fit in text field (256 characters)
            const truncatedAnalysis = analysis.length > 250
                ? analysis.substring(0, 250) + '...'
                : analysis;

            const updateData = {
                id: contactId,
                custom_fields_values: [{
                    field_id: parseInt(this.analysisReportFieldId),
                    values: [{
                        value: truncatedAnalysis
                    }]
                }]
            };

            const response = await this.makeRequest('contacts', 'PATCH', [updateData]);

            return {
                success: !!response._embedded?.contacts?.[0],
                externalId: contactId.toString(),
                data: response,
                fallback: true,
                truncated: analysis.length > 250
            };
        } catch (error) {
            log.error('Error updating contact analysis via text field', {
                error: error.message,
                contactId
            }, 'updateContactAnalysisTextField', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Format contact data for AmoCRM
     */
    formatContactData(data) {
        const contactData = {
            name: data.contact.name || 'Dialog User',
            custom_fields_values: []
        };

        // Note: Analysis report is now uploaded as a file, not stored in custom field

        // Add Telegram ID if field is configured
        if (this.telegramIdFieldId && data.contact.externalId) {
            contactData.custom_fields_values.push({
                field_id: parseInt(this.telegramIdFieldId),
                values: [{
                    value: data.contact.externalId
                }]
            });
        }

        // Add Telegram username if field is configured
        if (this.telegramUsernameFieldId && data.contact.name) {
            contactData.custom_fields_values.push({
                field_id: parseInt(this.telegramUsernameFieldId),
                values: [{
                    value: data.contact.name
                }]
            });
        }

        return contactData;
    }
}

export default AmoCRMIntegration;
