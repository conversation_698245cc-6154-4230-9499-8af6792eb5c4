// test-amocrm-integration.js
// Simple validation script for AmoCRM integration

import AmoCRMIntegration from './integrations/amocrm.js';

async function testAmoCRMIntegration() {
    console.log('Testing AmoCRM Integration...');

    // Test configuration
    const config = {
        domain: 'test.amocrm.ru',
        token: 'test-token',
        telegram_id_field_id: '123',
        telegram_username_field_id: '456',
        drive_url: 'https://drive-test.amocrm.ru'
    };

    try {
        // Test 1: Initialize integration
        console.log('\n1. Testing initialization...');
        const integration = new AmoCRMIntegration(config);
        console.log('✓ Integration initialized successfully');
        console.log(`   Domain: ${integration.domain}`);
        console.log(`   Drive URL: ${integration.driveUrl}`);

        // Test 2: Configuration validation
        console.log('\n2. Testing configuration validation...');
        const validationResult = integration.validateConfig(config);
        console.log(`✓ Configuration validation: ${validationResult.valid ? 'PASSED' : 'FAILED'}`);
        if (!validationResult.valid) {
            console.log(`   Errors: ${validationResult.errors.join(', ')}`);
        }

        // Test 3: File name generation
        console.log('\n3. Testing file name generation...');
        const fileName1 = integration.generateAnalysisFileName('Test User');
        const fileName2 = integration.generateAnalysisFileName('User@With#Special!Chars');
        console.log(`✓ Normal name: ${fileName1}`);
        console.log(`✓ Sanitized name: ${fileName2}`);

        // Test 4: Contact data formatting
        console.log('\n4. Testing contact data formatting...');
        const testData = {
            contact: {
                name: 'Test User',
                externalId: '12345'
            },
            analysis: 'This is a test analysis report that should be uploaded as a file.'
        };

        const contactData = integration.formatContactData(testData);
        console.log('✓ Contact data formatted successfully');
        console.log(`   Name: ${contactData.name}`);
        console.log(`   Custom fields count: ${contactData.custom_fields_values.length}`);
        
        // Check that analysis is not in custom fields
        const hasAnalysisField = contactData.custom_fields_values.some(
            field => field.field_id === integration.analysisReportFieldId
        );
        console.log(`   Analysis in custom fields: ${hasAnalysisField ? 'YES (unexpected)' : 'NO (correct)'}`);

        // Test 5: Configuration schema
        console.log('\n5. Testing configuration schema...');
        const schema = integration.getConfigSchema();
        console.log('✓ Configuration schema retrieved');
        console.log(`   Required fields: ${Object.keys(schema).filter(key => schema[key].required).join(', ')}`);
        console.log(`   Optional fields: ${Object.keys(schema).filter(key => !schema[key].required).join(', ')}`);

        // Test 6: Backward compatibility check
        console.log('\n6. Testing backward compatibility...');
        const legacyConfig = {
            domain: 'legacy.amocrm.ru',
            token: 'legacy-token',
            analysis_report_field_id: '789'
        };
        
        const legacyIntegration = new AmoCRMIntegration(legacyConfig);
        console.log('✓ Legacy configuration supported');
        console.log(`   Analysis field ID: ${legacyIntegration.analysisReportFieldId}`);
        console.log(`   Drive URL: ${legacyIntegration.driveUrl || 'Not set (will be auto-discovered)'}`);

        console.log('\n🎉 All tests completed successfully!');
        console.log('\nKey improvements implemented:');
        console.log('• Analysis reports are now uploaded as files instead of text fields');
        console.log('• Automatic drive URL discovery from AmoCRM account info');
        console.log('• Proper file naming with timestamps and sanitized contact names');
        console.log('• Backward compatibility with existing text field configuration');
        console.log('• Graceful fallback to text fields if file upload fails');
        console.log('• Enhanced error handling and logging for file operations');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error(error.stack);
    }
}

// Run the test
testAmoCRMIntegration().catch(console.error);
